<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<body>
	 <div class="modal-content">
                <div class="modal-header">
                    <div style="float:right; margin-top:-5px;">
                        <button type="button" class="btn btn-info" id="saveSysChickItemBtn" data-dismiss="modal">保存</button>
                        <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                        </div>
                    <h4 class="modal-title" id="myModalLabel">存为系统推荐模板</h4>
                </div>
                <form id="templeteForm">
                <div class="modal-body padding-lg" style="height:400px;">
                    <div class="smart-widget-body form-horizontal">
                        <div class="form-group">
                            <label class="col-lg-2 control-label"><span style="color: red;">*</span>名称</label>
                            <div class="col-lg-8">
                           <!--  <input id="industryTypeCode" name="templateIndustry" type="hidden">  -->
                            <input type="text"  id="templateName" name="templateName" class="form-control"  placeholder="名称">
                            </div>
                        </div>
                        <input type="hidden" id ="chickItem" name="chickItem" >
                         <div class="form-group">
                            <label class="col-lg-2 control-label">适用对象类型</label>
                            <div class="col-lg-8">
                            	 <select class="form-control" id="templateObjectType" name="templateObjectType">
                                <!--     <option value="">请选择</option> -->
                                    <option value="0">通用</option>
                                    <option value="1">企事业单位</option>
                                    <option value="2">个人</option>
                                    <option value="3">个人三无小三产</option>
                                    <option value="4">自然保护区</option>
                                    <option value="6">水源地</option>
                                    <option value="5">无主</option>
                                </select>
                            </div>
                        </div>
                         <div class="form-group" id="templateIndustryDiv">
                            <label for="行业类型" class="col-lg-2 col-md-2 control-label">适用行业</label>
                            <div class="col-lg-9">                            	
                                <div class="input-group" style="width:400px; float:left;">
								<input type="text" placeholder="请选择行业类型" readonly class="form-control" id='templateIndustryName1' name="templateIndustryName">
								<input type="hidden" id="templateIndustry1" name="templateIndustry">
									<div class="input-group-btn">
										<button type="button" class="btn btn-info no-shadow" tabindex="-1" data-toggle="modal" id="IndustryTypeChoose" 
										data-remote="${webpath}/refineTemplate/enterprises-industry-type-page?code=templateIndustry1&name=templateIndustryName1&all=allIndustry"
												data-target="#Industrytype">选择</button>
									</div>
								</div>
                                <div class="checkbox inline-block" style="padding-left:10px; float:left;">
                                    <div class="custom-checkbox">
                                        <input type="checkbox" id="allIndustry" name ="allIndustry">
                                        <label for="allIndustry" class="checkbox-blue" ></label>
                                    </div>
                                    <div class="inline-block vertical-top">
                                        	所有行业
                                    </div>
                                </div>
                                </div>
                                	
                            </div>
                        </div>
                    </div>
                </form>
                </div>
</body>
	<script type="text/javascript">
	   $("#allIndustry").click(function(){
	        if($('input[name="allIndustry"]').prop("checked"))
	        {
	            $("#templateIndustryName1").val("所有行业");
	            $("#templateIndustry1").val("all");
	        }else{
	        	 $("#templateIndustryName1").val("");
		         $("#templateIndustry1").val("");
	        }
	    })
	    
	    	//存为系统模板
		$("#saveSysChickItemBtn").click(function(){
			if( addSysChickItemVue.sysChickItem == null || addSysChickItemVue.sysChickItem.length==0){
				swal("提示","至少有选择一个检查项！", "info");
				return false;
			}
			$("#chickItem").val(JSON.stringify(addSysChickItemVue.sysChickItem));
			//判断模板的名称不能为空
			var templateName  =$("#templateName").val();
			if(templateName == '' || templateName == null){
				swal("提示 ", "模板的名称不能为空!", "info");
				return false;
			}
				var options = {
					url : WEBPATH + '/refineTemplate/saveItemTemplate',
					type : 'post',
					dataType:"json",
					success : function(data) {
						if(data.meta.result == 'success'){
							if(data.data.tempStatus=='1'){
								swal("提示 ",'该模板名称已被他人占用，请重新填写模板信息！', "info");
								return false;
							}else{
							 	swal({
									title : "提示",
									text : data.meta.message,
									type : "success",
								}, function(isConfirm) {
									business.addMainContentParserHtml( WEBPATH + '/refineTemplate/sysRefineTemplate',null);
								});
							}
						}else if(data.meta.code == '007'){
			                swal({ title : data.meta.message, text : "", type : "info" });
			            }else{
							swal("提示 ",data.meta.message, "error");
						}
					},
					error : function() {
						swal("提示 ", "保存模板信息失败!", "error");
					}
				}
				$('#templeteForm').ajaxSubmit(options);
		});
	   
		$(document).ready(function() {
			$('#cwxtmb').on('hide.bs.modal', function() {
				$(this).removeData("bs.modal");
			});
			
			$("#templateObjectType").change(function(){
				var templateObjectTypeTemp = $("#templateObjectType").val();
					if(templateObjectTypeTemp == 0 || templateObjectTypeTemp == 1){
						$("#templateIndustryDiv").show();
					}else{
						$("#templateIndustryDiv").hide();
						$("#templateIndustry1").val("");
						$("#templateIndustryName1").val("");
					}
				})
		})

	</script>
</html> 
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags" %>  
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<sec:authentication property="principal" var="authentication"/>

<style>
<!--
 
-->
table-bt-hover{
	cursor:pointer;
}
</style>

<script type="text/javascript">
$(document).ready(function(){
	// 注释 模态框清理 
 	$('#askCustomModel').on('hide.bs.modal', function () {
		   $(this).removeData("bs.modal");  
	}) 
	
})
	$('#askCustomModelTable').bootstrapTable({       
		 method: 'post',
		 dataType: "json", 
		 url:  WEBPATH+'/askingManager/ask-custom-list',
	     undefinedText : '-',  
	     pagination : true, // 分页  
	     striped : true, // 是否显示行间隔色  
	     cache : false, // 是否使用缓存  
	     pageSize:10, // 设置默认分页为 20
	     pageNumber: 1,
	     queryParamsType: "",
	     locale:'zh-CN',
	     pageList: [5, 10, 20,30,50], // 自定义分页列表
	     singleSelect: false,
	     contentType: "application/x-www-form-urlencoded;charset=UTF-8",
	     // showColumns : true, // 显示隐藏列  
	     sidePagination: "server", //服务端请求
	     queryParams:function (params) {
	      		var type = '${lawObj.lawObjectType}';
	      		var templateName = $("#templateName").val();
	      		var contributionName = $("#askModelerContributionName").val();
	      		var templateNumber = $("#templateNumber").val();
	      		var belongCity = $("#belong_city").val();
	      		var belongCountry = $("#belong_country").val();
	            var temp = {   //这里的键的名字和控制器的变量名必须一直，这边改动，控制器也需要改成一样的
           		 	pageNum: params.pageNumber,
                    pageSize: params.pageSize,
                    type:type,
                    belongCity:belongCity,
                    belongCountry:belongCountry,
                    templateName:templateName,
                    contributionName:contributionName,
                    templateNumber:templateNumber
	            };
	            return temp;
	     },//参数
	     uniqueId : "id", // 每一行的唯一标识  
		 columns: [
				   {
		        	   field: "templateNumber",
			           title: "模板编号",
			           align: 'center'
		           },
		           {
			           field: "templateName",
			           title: "名称",
			           align: 'center'
			       },
		           {
			           field: "templateAreaname",
			           title: "适用地区",
			           align: 'center'
		           },
		           {  
			           field: "",
			           title: "适用对象类型",
			           align: 'center',
			           formatter: function(value,row,index){
			        	   var html = "";
			        	   if(row.templateObjectType=='1'){
			        		   html="企事业单位";
			        	   }else if(row.templateObjectType=='2'){
			        		   html="个人";
			        	   }else if(row.templateObjectType=='3'){
			        		   html="个体、三无、小三产";
			        	   }else if(row.templateObjectType=='4'){
			        		   html="自然保护区";
			        	   }else if(row.templateObjectType=='6'){
                               html="水源地";
                           }else if(row.templateObjectType=='5'){
			        		   html="无主";
			        	   }else{
			        		   html="通用";
			        	   }
				           return html;
				       }
		           },
		           {
			           field: "templateIndustryName",
			           title: "适用行业",
			           align: 'center'
		           },
		           {
			           field: "createTime",
			           title: "创建日期",
			           align: 'center',
			   		   formatter : function(value){
							if(value==null || value==''){return '';}
			                var date = new Date(value);
			                var y = date.getFullYear();
			                var m = date.getMonth() + 1;
			                var d = date.getDate();
			                var h = date.getHours();  
			                var i = date.getMinutes(); //分
			                var s = date.getSeconds(); //秒
			                return y + '-' +m + '-' + d+' '+h+':'+i+':'+s;
			            }
		           },
		           {
			           field: "",
			           title: "贡献者",
			           align: 'center',
			           width: '150px',
			           formatter:function(value,row,index){
			        	   var html= row.contributionName;
			        	   if(row.contributionName!=null && row.contributionName.length >50){
			        		   var txt = row.contributionName.substring(0,50)+"..."
			        		   html ="<span title=\""+row.contributionName+"\" >"+txt+"</span>";
			        	   }
			        	   return html;
			           }
		           },
		           {    
		        	   field: '',
			           title: "操作",
			           align: 'center',
			           width: '120px',
			           formatter: function(value,row,index){
				          var userId = '${authentication.id}';
			        	  var html="";
			           	  //html+="<a tabindex='-1' data-toggle='modal' data-remote='${webpath}/refineTemplate/sysRefineTemplate-view-model?id="+row.id+"&modelType=2&titleName=1"+"' data-target='#mbyl'  style=\"cursor:pointer;\"><i class=\"fa fa-search\" style=\"color: #23b7e5;\">预览</i></a>&nbsp";
			        	  html+="<a tabindex='-1' data-toggle='modal' data-remote='${webpath}/inquiryRecordTemplate/xwbl-refineTemplate-view-model?id="+row.id+"&modelType=2&titleName=1"+"' data-target='#mbyl'  style=\"cursor:pointer;\"><i class=\"fa fa-search\" style=\"color: #23b7e5;\">预览</i></a>&nbsp";
			           	  if(row.aumUserId == userId){ // 常用
			        		  // 已经设置为常用
			        		  html+="<i class=\"fa fa-file-text-o\" style=\"color:#23b7e5;cursor:pointer;\"  onclick=\"usuallyOrDefaultOrDeleteModeler('"+row.id+"','1','"+row.templateObjectType+"','0')\"  > 常用</i>"; 
			        	  }else{
			        		  html+="<i class=\"fa fa-file-text-o\" style=\"color:#ccc;cursor:pointer;\"  onclick=\"usuallyOrDefaultOrDeleteModeler('"+row.id+"','0','"+row.templateObjectType+"','0')\"  > 常用</i>";
			        	  }
			        	  if(row.admUserId == userId ){// 默认
			        		  // 已经设置为默认
			        		  html+="<i class=\"fa fa-file-word-o\" style=\"color:#23b7e5;cursor:pointer;\" onclick=\"usuallyOrDefaultOrDeleteModeler('"+row.id+"','1','"+row.templateObjectType+"','1')\"  > 默认加载模板</i>";
			        	  }else{
			        		  html+="<i class=\"fa fa-file-word-o\" style=\"color:#ccc;cursor:pointer;\" onclick=\"usuallyOrDefaultOrDeleteModeler('"+row.id+"','0','"+row.templateObjectType+"','1')\"  > 默认加载模板</i>";
			        	  }
			        	  if(row.userId == userId){// 创建者
			        		  html+="<i class=\"fa fa-times\" style=\"color:red;cursor:pointer;\" onclick=\"usuallyOrDefaultOrDeleteModeler('"+row.id+"','','"+row.templateObjectType+"','2')\"  >删除</i>";
			        	  }
			        	  return html;
				       }
		           }
		 ],
		 responseHandler : function(res) {  
              return {  
                  total : res.total,  
                  rows : res.list  
              };  
        },
        rowStyle:function(row,index) {
        	return {
        		   css: {"cursor": "pointer"}
        		 };
        },
        onCheck: function(row, $element) {
   	   
        },//单击row事件
        onUncheck: function(row, $element) {
       		
	     },
	     onUncheckAll: function(row, $element) {
	       			
	     },
	     onCheckAll:function(row, $element) {
	        		
	     },
	     onRefresh: function () {
	        		
	     }, // 双击事件
	     onDblClickRow: function(row, $element){
	    	 // 1. 关闭模态框  2.发送id到后端组装vue数据
	    	 var obj={code:1,customModelerId:row.id};
	    	 $.ajax({
				cache : true,
				type : "POST",
				url : WEBPATH + '/askingManager/ask-content-vue',
				data:obj, 
				async : false,
				error : function(request) {
					swal("错误!","获取模板项失败！", "error");
				},
				success : function(data) {
					if(dataArr!= null){
						dataArr.splice(0,dataArr.length);
						for(var i =0;i<data.contentList.length;i++){
							 dataArr.push(data.contentList[i]);
						} 
					}else{
						dataArr = data.contentList;
					}
					//vueContentTables.items= data.list;
					$("#contributionName").val(data.recordName);  
					$("#askModelerType").html('('+data.modelerType+')');
					
					//$("#templateContributionName").val(data.recordName);  
				}
			 });
	    	 $('#askCustomModel').modal('hide');
	    	 
	     },
        formatLoadingMessage: function () {
       	   return "玩命加载中...";
        },
        formatNoMatches: function () { //没有匹配的结果
       		   return '无符合条件的记录';
        }
	});
	//绑定搜索按钮
	$('#askCustomModelSearchButt').click(function() {
		//$('#askCustomModelTable').bootstrapTable('refresh');
		$('#askCustomModelTable').bootstrapTable('refreshOptions',{pageNumber:1,pageSize:15});
  	});
</script>
<!-- 省市县三级级联 -->
<script type="text/javascript">
$(document).ready(function(){
	var htmlCity = "<option value=''>请选择</option>"; 
	var htmlCounty = "<option value=''>请选择</option>"; 
    $.ajax({
        type:"post",
        url:WEBPATH+"/tArea/cityList",
        dataType:"json",
        success:function(data){
        	 $("#belong_city").html(htmlCity);
        	 $("#belong_country").html(htmlCounty);
	         $.each(data,function(i,item){
	        	   $("#belong_city").append("<option value="+item.code+"  >"+item.name+"</option>"); 
	         });
        }
    });
 	// 市联动县
	$("#belong_city").change(function(){
		if ($(this).val() == ""){
			$("#belong_country option").remove();
			$("#belong_country").html(htmlCounty);
			return;
		}
		var parentCode = $(this).val();
		$("#belong_country option").remove();
		$.ajax({
			type:"post",
			url:WEBPATH+"/tArea/countyListByCode",
			async:false,
			dataType:"json",
			data:{parentCode:parentCode},
			success:function(data){
				$("#belong_country").html(htmlCounty);
				$.each(data,function(i,item){
					$("#belong_country").append("<option value="+item.code+"  >"+item.name+"</option>"); 
				});
			}
		});
	});
 
});
</script>

<!-- 页面主动触发方法 -->
<script type="text/javascript">

 // 设置和解除常用 modelerID:模板id    code：1->当前已经是常用 0->当前不是常用      type: 行业类型      usuallyOrDefaultOrDelete：0 常用  1默认  2删除
 function usuallyOrDefaultOrDeleteModeler(modelerID,code,type,usuallyOrDefaultOrDelete){
	 if(type ==null || type=='null'){
			type= '';
		}
	 var obj={modelerId:modelerID,code:code,type:type,usuallyOrDefaultOrDelete:usuallyOrDefaultOrDelete};
	 var titleHtml = "";
	 var textHtml = "";
	 var confirmButtonTextHtml = "设置";
	 if(usuallyOrDefaultOrDelete == 0){
		 titleHtml ="设置常用模板";
		 if(code==1){
			 textHtml ="您是否要取消该项为常用模板？";
		 }else{
			 textHtml ="您是否要设置该项为常用模板？";
		 }
	 }else if (usuallyOrDefaultOrDelete == 1){
		 titleHtml ="设置默认模板";
		 if(code==1){
			 textHtml ="您是否要取消该项为默认模板？";
		 }else{
			 textHtml ="您是否要设置该项为默认模板？";
		 }
	 }else if (usuallyOrDefaultOrDelete == 2){
		 titleHtml ="删除模板";
		 textHtml ="您确定要删除该模板？";
		 confirmButtonTextHtml = "删除";
	 }else{
		 swal({title: "操作项不明确" ,text: "",type:"error"});
		 return ;
	 }
	 swal({
			title : titleHtml,
			text : textHtml,
			type : "warning",
			showCancelButton : true,
			confirmButtonColor : "#DD6B55",
			confirmButtonText : "是的，我要"+confirmButtonTextHtml+"!",
			cancelButtonText : "让我再考虑一下",
			closeOnConfirm : false,
			closeOnCancel : false
		},
		function(isConfirm) {
			if (isConfirm) {
				$.ajax({
	                cache: true,
	                type: "POST",
	                url: WEBPATH+'/askingManager/usually-or-default-or-delete',
	                data:obj,
	                async: false,
	                error: function(request) {
	                    swal({title: "设置失败" ,text: "",type:"error"});
	                },
	                success: function(data) {
	                  	if(data.result=='success'){
	                  		swal({title: "设置成功" ,text: "",type:data.result});
	                  	}else{
	                  		swal({title: data.message ,text: "",type:data.result});
	                  	}
	                }
	            });
				// 刷新列表
				$('#askCustomModelTable').bootstrapTable('refresh');
			}else {
				swal({
					title : "已取消",
					text : "您已取消当前操作！",
					type : "error"
				})
		}
	})
 }
</script>


<div class="modal-header">
	<button type="button" class="close" data-dismiss="modal"
		aria-hidden="true">&times;</button>
	<h4 class="modal-title" id="myModalLabel">自定义模板</h4>
</div>
<div class="modal-body">
	<div class="smart-widget-body form-horizontal">
				<form  id= "TaskGeneralSearchForm"> 
                    <div class="form-group">
                        <label class="col-lg-2 control-label">名称</label>
                        <div class="col-lg-3">
                        <input type="hbfzrdh" class="form-control" id="templateName" placeholder="名称">
                        </div>
                        <label class="col-lg-2 control-label">贡献者</label>
                        <div class="col-lg-3">
                        <input type="hbfzrdh" class="form-control" id="askModelerContributionName" placeholder="贡献者">
                        </div>
                    </div>
                    <div class="form-group">
                    	 <label class="col-lg-2 control-label">模板编号</label>
                        <div class="col-lg-8">
                        <input type="hbfzrdh" class="form-control" id="templateNumber" placeholder="模板编号">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-lg-2 control-label">适用地区</label>
                        <div class="col-lg-2">
                            <select class="form-control"  disabled="true" >
                                <option value="35000000">福建省</option>
                            </select>
                        </div>
                        <div class="col-lg-3">
                            <select class="form-control" id="belong_city">
                            </select>
                         </div>
                          <div class="col-lg-3">
                            <select class="form-control" id="belong_country">
                                 
                            </select>
                        </div>
                     </form>  
                        <button type="button" class="btn btn-info" id="askCustomModelSearchButt">查询</button>
                    </div>
               <div style="margin:10px 20px; color:red;">提示：双击模板列表行进行选择</div>
		<table  class="table table-striped no-margin table-no-bordered"  id="askCustomModelTable">
		 
		</table>
	</div>
</div>
<div class="modal-footer" style="margin: 40px 0 0 0;">
	<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
</div>

package org.changneng.framework.frameworkcore;

//import com.lowagie.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfReader;
import junit.framework.Test;
import junit.framework.TestCase;
import junit.framework.TestSuite;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * Unit test for simple App.
 */
public class AppTest {
    public static void main(String[] args) {
        String code = null;
        try {
            LinkedHashSet<String> set = new LinkedHashSet<String>();
            set.add("1");
            set.add("2");
            set.add("3");
            set.add("4");
            set.add("5");
            set.add("6");
            set.add("7");
            set.add("8");
            set.add("9");
            set.add("10");
            set.add("11");
            set.add("12");
            set.add("13");

            //获取第一个文号值
            LinkedList<String> linkedList = new LinkedList<>();

            linkedList.addAll(set);
            code = null;
            for(int i=0;i<linkedList.size();i++){
                code = linkedList.get(i);
//                boolean b = stringRedisTemplate.opsForValue().setIfAbsent(areaCode+type+code, "Lock");
//                if(b){
//                    code= linkedList.get(i);
//                    stringRedisTemplate.opsForValue().set(areaCode+type+code, "firstLock", 1, TimeUnit.HOURS);
//                    break;
//                }else{
//                    if(i==linkedList.size()-1){
//                        //已经到了最后一个code，需要单独增加一个，这时候集合会超过100临界值
//                        code = String.valueOf(Integer.valueOf(stringRedisTemplate.opsForValue().get(areaCode+type))+1);
//                        stringRedisTemplate.opsForValue().set(areaCode+type, code);
//                        //放到集合中
//                        stringRedisTemplate.opsForZSet().add(areaCode+type+"Set", code, Double.valueOf(code));
//                        boolean b2 = stringRedisTemplate.opsForValue().setIfAbsent(areaCode+type+code, "Lock");
//                        if(b2){
//                            stringRedisTemplate.opsForValue().set(areaCode+type+code, "firstLock", 1, TimeUnit.HOURS);
//                            return code;
//                        }else{
//                            continue;
//                        }
//
//
//                    }
//                }
            }
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }
    }
}

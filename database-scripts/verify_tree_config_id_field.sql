-- =====================================================
-- 验证检查项配置树形结构ID字段
-- 文件名: verify_tree_config_id_field.sql
-- 创建日期: 2025-02-01
-- 描述: 验证LOCAL_CHECK_ITEM表中TREE_CONFIG_ID字段是否正确添加
-- =====================================================

-- 1. 检查字段是否存在
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    DATA_LENGTH,
    NULLABLE,
    DATA_DEFAULT
FROM USER_TAB_COLUMNS 
WHERE TABLE_NAME = 'LOCAL_CHECK_ITEM' 
AND COLUMN_NAME = 'TREE_CONFIG_ID';

-- 2. 检查字段注释
SELECT 
    COLUMN_NAME,
    COMMENTS
FROM USER_COL_COMMENTS 
WHERE TABLE_NAME = 'LOCAL_CHECK_ITEM' 
AND COLUMN_NAME = 'TREE_CONFIG_ID';

-- 3. 检查表结构（显示所有字段）
DESC LOCAL_CHECK_ITEM;

-- 4. 测试插入数据（可选）
/*
INSERT INTO LOCAL_CHECK_ITEM (
    ID, CHECK_ITEM_NAME, CHECK_ITEM_RESULT, LOCAL_CHECK_ID,
    FORM_TYPE, CONFIG_ITEM_ID, TREE_CONFIG_ID, PROBLEM_DESC,
    CREATE_TIME, UPDATE_TIME
) VALUES (
    'TEST_' || SYS_GUID(), 
    '测试检查项', 
    '1', 
    'TEST_LOCAL_CHECK_ID',
    1, 
    'TEST_CONFIG_ID', 
    'TEST_TREE_CONFIG_ID', 
    '测试问题简述',
    SYSDATE, 
    SYSDATE
);

-- 验证插入的数据
SELECT ID, CHECK_ITEM_NAME, CONFIG_ITEM_ID, TREE_CONFIG_ID, FORM_TYPE
FROM LOCAL_CHECK_ITEM 
WHERE ID LIKE 'TEST_%'
ORDER BY CREATE_TIME DESC;

-- 清理测试数据
DELETE FROM LOCAL_CHECK_ITEM WHERE ID LIKE 'TEST_%';
COMMIT;
*/

-- 5. 检查现有数据中TREE_CONFIG_ID字段的情况
SELECT 
    FORM_TYPE,
    COUNT(*) AS TOTAL_COUNT,
    COUNT(TREE_CONFIG_ID) AS HAS_TREE_CONFIG_ID_COUNT,
    COUNT(*) - COUNT(TREE_CONFIG_ID) AS NULL_TREE_CONFIG_ID_COUNT
FROM LOCAL_CHECK_ITEM 
GROUP BY FORM_TYPE
ORDER BY FORM_TYPE;

-- 6. 显示环境监管一件事类型的检查项（FORM_TYPE=1）
SELECT 
    ID,
    CHECK_ITEM_NAME,
    CONFIG_ITEM_ID,
    TREE_CONFIG_ID,
    FORM_TYPE,
    CREATE_TIME
FROM LOCAL_CHECK_ITEM 
WHERE FORM_TYPE = 1
ORDER BY CREATE_TIME DESC
FETCH FIRST 10 ROWS ONLY;

# LOCAL_CHECK_ITEM表添加TREE_CONFIG_ID字段实施指南

## 📋 概述

本文档描述了在LOCAL_CHECK_ITEM表中添加TREE_CONFIG_ID字段的完整实施方案，用于存储检查项配置的树形结构ID，建立与CHECK_ITEM_CONFIG表的直接关联关系。

## 🎯 实施目标

1. **新字段添加**：在LOCAL_CHECK_ITEM表中添加TREE_CONFIG_ID字段
2. **关联关系建立**：该字段关联CHECK_ITEM_CONFIG表的主键ID
3. **排序功能优化**：使用新字段实现检查项的树形结构排序
4. **向后兼容性**：确保原有功能不受影响

## 📊 字段定义

| 属性 | 值 |
|------|-----|
| 字段名 | TREE_CONFIG_ID |
| 数据类型 | VARCHAR2(100) |
| 允许为空 | YES |
| 默认值 | NULL |
| 注释 | 检查项配置树形结构ID，关联CHECK_ITEM_CONFIG表主键 |

## 🗄️ 数据库变更

### 1. DDL脚本执行

```bash
# 执行字段添加脚本
sqlplus username/password@database @add_tree_config_id_field.sql
```

### 2. 数据迁移（可选）

```bash
# 执行数据迁移脚本
sqlplus username/password@database @migrate_tree_config_id_data.sql
```

### 3. 验证脚本

```bash
# 执行验证脚本
sqlplus username/password@database @verify_tree_config_id_field.sql
```

## 💻 代码变更

### 1. 实体类修改

**文件：** `LocalCheckItem.java`
- 添加`treeConfigId`字段
- 添加对应的getter和setter方法

### 2. Mapper.xml修改

**文件：** `LocalCheckItemMapper.xml`
- 更新Base_Column_List
- 修改所有insert、update语句
- 添加TREE_CONFIG_ID字段处理

### 3. 业务逻辑修改

**文件：** `LocalExamineServiceImpl.java`
- 修改排序逻辑使用`treeConfigId`字段
- 在保存环境监管一件事数据时设置`treeConfigId`值

## 🔄 使用场景

### 1. 保存环境监管一件事数据

当`formType=1`时，系统会：
- 将`configItemId`的值同时设置到`treeConfigId`字段
- 用于后续的排序和层级显示

### 2. 检查项排序

系统使用`treeConfigId`字段：
- 关联CHECK_ITEM_CONFIG表获取树形结构
- 按照树形结构的层级和排序进行排列
- 保持平铺列表格式

### 3. 向后兼容

- 原有检查项（`formType=0`）：`treeConfigId`字段为空，不影响现有功能
- 环境监管一件事（`formType=1`）：使用新字段进行排序

## ⚠️ 注意事项

### 1. 数据一致性

- 对于环境监管一件事类型的检查项，`configItemId`和`treeConfigId`应该保持一致
- 新增数据时系统会自动设置这两个字段的值

### 2. 性能考虑

- 新字段为VARCHAR2(100)，与CHECK_ITEM_CONFIG表主键类型一致
- 建议在生产环境中为该字段创建索引（如果查询频繁）

### 3. 测试验证

- 执行完整的回归测试
- 验证原有检查项功能正常
- 验证环境监管一件事排序功能正常

## 📝 实施检查清单

- [ ] 执行DDL脚本添加字段
- [ ] 执行数据迁移脚本（如有需要）
- [ ] 验证字段添加成功
- [ ] 部署代码变更
- [ ] 执行功能测试
- [ ] 验证排序功能正常
- [ ] 确认向后兼容性

## 🔧 回滚方案

如果需要回滚：

1. **代码回滚**：恢复到变更前的代码版本
2. **数据库回滚**：
   ```sql
   -- 删除新添加的字段（谨慎操作）
   ALTER TABLE LOCAL_CHECK_ITEM DROP COLUMN TREE_CONFIG_ID;
   ```

## 📞 联系信息

如有问题，请联系开发团队。

---

**创建日期：** 2025-02-01  
**版本：** 1.0  
**状态：** 待实施

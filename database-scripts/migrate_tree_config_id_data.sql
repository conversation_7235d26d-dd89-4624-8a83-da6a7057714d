-- =====================================================
-- 迁移检查项配置树形结构ID数据
-- 文件名: migrate_tree_config_id_data.sql
-- 创建日期: 2025-02-01
-- 描述: 为现有的环境监管一件事检查项设置TREE_CONFIG_ID字段值
-- =====================================================

-- 1. 备份现有数据（可选）
-- CREATE TABLE LOCAL_CHECK_ITEM_BACKUP_TREE_CONFIG AS 
-- SELECT * FROM LOCAL_CHECK_ITEM WHERE FORM_TYPE = 1;

-- 2. 检查需要迁移的数据
SELECT 
    COUNT(*) AS TOTAL_ENV_SUPERVISION_ITEMS,
    COUNT(CONFIG_ITEM_ID) AS HAS_CONFIG_ITEM_ID,
    COUNT(TREE_CONFIG_ID) AS HAS_TREE_CONFIG_ID
FROM LOCAL_CHECK_ITEM 
WHERE FORM_TYPE = 1;

-- 3. 显示需要迁移的数据样例
SELECT 
    ID,
    CHECK_ITEM_NAME,
    CONFIG_ITEM_ID,
    TREE_CONFIG_ID,
    CREATE_TIME
FROM LOCAL_CHECK_ITEM 
WHERE FORM_TYPE = 1 
AND CONFIG_ITEM_ID IS NOT NULL 
AND TREE_CONFIG_ID IS NULL
ORDER BY CREATE_TIME DESC
FETCH FIRST 10 ROWS ONLY;

-- 4. 执行数据迁移
-- 将CONFIG_ITEM_ID的值复制到TREE_CONFIG_ID字段
UPDATE LOCAL_CHECK_ITEM 
SET TREE_CONFIG_ID = CONFIG_ITEM_ID
WHERE FORM_TYPE = 1 
AND CONFIG_ITEM_ID IS NOT NULL 
AND TREE_CONFIG_ID IS NULL;

-- 5. 显示迁移结果
SELECT 
    '迁移完成' AS STATUS,
    COUNT(*) AS UPDATED_RECORDS
FROM LOCAL_CHECK_ITEM 
WHERE FORM_TYPE = 1 
AND CONFIG_ITEM_ID IS NOT NULL 
AND TREE_CONFIG_ID IS NOT NULL
AND CONFIG_ITEM_ID = TREE_CONFIG_ID;

-- 6. 验证迁移结果
SELECT 
    FORM_TYPE,
    COUNT(*) AS TOTAL_COUNT,
    COUNT(CONFIG_ITEM_ID) AS HAS_CONFIG_ITEM_ID_COUNT,
    COUNT(TREE_CONFIG_ID) AS HAS_TREE_CONFIG_ID_COUNT,
    SUM(CASE WHEN CONFIG_ITEM_ID = TREE_CONFIG_ID THEN 1 ELSE 0 END) AS MATCHING_IDS_COUNT
FROM LOCAL_CHECK_ITEM 
WHERE FORM_TYPE = 1
GROUP BY FORM_TYPE;

-- 7. 显示迁移后的数据样例
SELECT 
    ID,
    CHECK_ITEM_NAME,
    CONFIG_ITEM_ID,
    TREE_CONFIG_ID,
    CASE 
        WHEN CONFIG_ITEM_ID = TREE_CONFIG_ID THEN '匹配'
        WHEN CONFIG_ITEM_ID IS NULL AND TREE_CONFIG_ID IS NULL THEN '都为空'
        ELSE '不匹配'
    END AS ID_MATCH_STATUS,
    CREATE_TIME
FROM LOCAL_CHECK_ITEM 
WHERE FORM_TYPE = 1
ORDER BY CREATE_TIME DESC
FETCH FIRST 10 ROWS ONLY;

-- 提交事务
COMMIT;

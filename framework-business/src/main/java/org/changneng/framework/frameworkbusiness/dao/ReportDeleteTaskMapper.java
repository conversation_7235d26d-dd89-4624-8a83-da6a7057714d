package org.changneng.framework.frameworkbusiness.dao;

import org.changneng.framework.frameworkbusiness.entity.ReportDeleteTask;

import java.util.List;

public interface ReportDeleteTaskMapper {
    int deleteByPrimaryKey(String id);

    int insert(ReportDeleteTask record);

    int insertSelective(ReportDeleteTask record);

    ReportDeleteTask selectByPrimaryKey(String id);

    List<ReportDeleteTask> selectIsdel();

    int updateByPrimaryKeySelective(ReportDeleteTask record);

    int updateByPrimaryKey(ReportDeleteTask record);

    /**
     * 更改报备表中数据为已删除
     * 
     * <AUTHOR>
     */
	void updateDataToDel();

}
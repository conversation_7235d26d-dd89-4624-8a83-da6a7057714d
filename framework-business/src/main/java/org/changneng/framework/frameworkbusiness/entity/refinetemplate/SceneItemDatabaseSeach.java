package org.changneng.framework.frameworkbusiness.entity.refinetemplate;

public class SceneItemDatabaseSeach {
    private String id;

    private String checkItemName;//检查内容

    private String checkItemStatus;//检查项类型
    private String checkitemType;//检查项状态 用于判断单选、下拉
    
    private String createStartTime;//创建日期 起始
    
    private String createEndTime;//创建日期 结束
    
    private int pageNumber;
    
    
    private String behId;//违法名称id
    private String behFact;//违法名称
    
    private String onlyMe;//只显示我的
    
    private String createUserId;//创建人id
    
    private String createUserName;//贡献者
    
	public int getPageNumber() {
		return pageNumber;
	}

	public void setPageNumber(int pageNumber) {
		this.pageNumber = pageNumber;
	}

	public String getBehId() {
		return behId;
	}

	public void setBehId(String behId) {
		this.behId = behId;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getCheckItemName() {
		return checkItemName;
	}

	public void setCheckItemName(String checkItemName) {
		this.checkItemName = checkItemName;
	}

	public String getCheckItemStatus() {
		return checkItemStatus;
	}

	public void setCheckItemStatus(String checkItemStatus) {
		this.checkItemStatus = checkItemStatus;
	}

	public String getCheckitemType() {
		return checkitemType;
	}

	public void setCheckitemType(String checkitemType) {
		this.checkitemType = checkitemType;
	}

	public String getCreateStartTime() {
		return createStartTime;
	}

	public void setCreateStartTime(String createStartTime) {
		this.createStartTime = createStartTime;
	}

	public String getCreateEndTime() {
		return createEndTime;
	}

	public void setCreateEndTime(String createEndTime) {
		this.createEndTime = createEndTime;
	}

	public String getBehFact() {
		return behFact;
	}

	public void setBehFact(String behFact) {
		this.behFact = behFact;
	}

	public String getOnlyMe() {
		return onlyMe;
	}

	public void setOnlyMe(String onlyMe) {
		this.onlyMe = onlyMe;
	}

	public String getCreateUserId() {
		return createUserId;
	}

	public void setCreateUserId(String createUserId) {
		this.createUserId = createUserId;
	}

	public String getCreateUserName() {
		return createUserName;
	}

	public void setCreateUserName(String createUserName) {
		this.createUserName = createUserName;
	}

    
    
    
}
package org.changneng.framework.frameworkbusiness.service;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.changneng.framework.frameworkbusiness.entity.*;
import org.changneng.framework.frameworkbusiness.entity.filecase.CFileCheck;
import org.changneng.framework.frameworkbusiness.entity.filecase.CFileDictionary;
import org.changneng.framework.frameworkcore.exception.BusinessException;
import org.changneng.framework.frameworkcore.utils.ResponseJson;

public interface ITmEvidenceCollectService {
	
	
	List<PicEvidence> queryPicEvidenceList(String taskId);
	
	List<AttachmentEvidence> queryAttachmentEvidenceListByType(String taskId);
	
	PicEvidence  queryPicEvidence(String id);
	/**
	 * 根据id查询录音信息
	 * @param id
	 * @return
	 */
	RecordEvidence  queryRecordEvidence(String id);
	
	/**
	 * 根据任务id查询录音信息列表
	 * @param id
	 * @return
	 */
	List<RecordEvidence>  queryRecordEvidenceList(String taskId);
	
	/**
	 * 根据id查询录像信息
	 * @param id
	 * @return
	 */
	VideoEvidence  queryVideoEvidence(String id);
	
	/**
	 * 根据任务id查询录像信息列表
	 * @param id
	 * @return
	 */
	List<VideoEvidence>  queryVideoEvidenceList(String taskId);
	
	Task queryTaskById(String id);
	
	AttachmentEvidence queryAttachmentEvidence(String id);
	
	SysFiles querySysFilesById(String id);
	
	void taskManagerXczfZjcjSave(String taskId,List<SysFiles> list) throws BusinessException;
	
	void taskManagerXczfZjfjSave(String taskId,String code,String name,List<SysFiles> list) throws BusinessException;
	
	void taskManagerXczfSpzjOrYpzjSave(String taskId,List<SysFiles> list,String fileType) throws BusinessException;
	
	int  updatepicinfo(PicEvidence pe,int degree, SysUsers suers) throws BusinessException;
	
	void deletepicInfo(String ids) throws BusinessException;
	
	void deletefileInfo(String id) throws BusinessException;
	
	void deletefileInfoBach(String id) throws BusinessException;
	
	List<SysFiles>  uploadPic(HttpServletRequest request,HttpServletResponse response,SysUsers sysUsers) throws BusinessException;
	
	List<SysFiles>  uploadPicForTask(HttpServletRequest request,HttpServletResponse response,SysUsers sysUsers, AttachmentEvidence attachmentEvidence) throws BusinessException;

	//效能上传打卡
	List<XnFilesInfo>  uploadPdfForXn(HttpServletRequest request, HttpServletResponse response, SysUsers sysUsers, AttachmentEvidence attachmentEvidence) throws BusinessException;
	
	ResponseJson rotateImage(String id,String url,String tableName,Integer degree);
	/**
	 * 
	 * @param request
	 * @param response
	 * @param fileType 上传文件类型 3 录像 4 音频
	 * @param sysUsers
	 * @return
	 * @throws BusinessException
	 */
	List<SysFiles>  uploadVideoOrRecord(HttpServletRequest request,HttpServletResponse response,String fileType,SysUsers sysUsers ) throws BusinessException;
	
	/**
	 * 批量更新图片信息
	 * @param ids
	 * @param pe
	 * @throws BusinessException
	 */
	void updatepicInfoBatch(String ids,PicEvidence pe) throws BusinessException;
	

	/**
	 * 图片pdf文件生成器
	 * @param ids
	 * @return
	 */
	String picsPDFservice(String ids) throws Exception;
	/**
	 * app文件保存返回文件信息 附件
	 * @param response 
	 * @param request 
	 * @param taskId
	 * @param code
	 * @param name
	 * @param list
	 * @param sysUsers 
	 * @return
	 */
	AppFileItem taskManagerFileSave(HttpServletRequest request, HttpServletResponse response, String taskId, String code,
			String name,SysUsers sysUsers);
	/**
	 * app文件保存返回文件信息 图片
	 * @param taskId
	 * @param list
	 * @return
	 */
	AppFileItem taskManagerFileImgjSave(String taskId, SysFiles list);
	
	/**
	 *上传保存文件
	 * @param request
	 * @param response
	 * @param sysUsers
	 * @return
	 * @throws Exception 
	 */
	AppFileItem fileUpload(HttpServletRequest request,
			HttpServletResponse response, SysUsers sysUsers,String taskId) throws Exception;
	/**
	 * 根据任务id查询照片证据扫描件
	 * @param taskId
	 * @return
	 */
	List<ScanningAttachment> queryAttachmentByTaskId(String taskId);
	
	/**
	 * 
		批量更新录像信息
	 * @param ids
	 * @param pe
	 */
	void updateVoideInfoBatch(String ids, VideoEvidence ve)  throws BusinessException ;
	/**
	 * 
		批量更新录音信息
	 * @param ids
	 * @param re
	 */
	void updateRecordInfoBatch(String ids, RecordEvidence ve)  throws BusinessException ;
	
	/**
	 * 根据id查询 视频录像
	 * @param id
	 * @return
	 */
/*	VideoEvidence queryVideoEvidence(String id);
	*//**
	 * 根据任务的id查询视频录像
	 * @param taskId
	 * @return
	 *//*
	List<VideoEvidence> queryVideoEvidenceList(String taskId);

*/
	/**
	 * 根据组合主键id删除音频信息
	 * @param ids
	 * @throws BusinessException
	 */
	void deleteRecordInfo(String ids) throws BusinessException;
	
	/**
	 * 根据组合主键id删除视频信息
	 * @param ids
	 * @throws BusinessException
	 */
	void deleteVideoInfo(String ids) throws BusinessException;

	/**
	 * 
	 * 执法文书数据加载
	 */
	List<CFileDictionary> getCFileDictionaryList(String codes);
	
	/**
	 * 
	 * 通过任务ID加载执法文书数据,用于存在附件但类别未启用的条目
	 */
	List<AttachmentEvidence> getAttachmentEvidenceListByTaskId(String tsakId);
	
	/**
	 * 
	 * 通过字典表CODE加载执法文书数据,用于返回当前类别下附件的的file_id
	 */
	List<AttachmentEvidence> getAttachmentEvidenceListByCode(String taskId, String code);
	
	/**
	 * 
	 * 通过file_id加载SysFiles数据，获取文件大小字段
	 */
	SysFiles getSysFilesById(String id);
	
	void extractFileInfo(String taskId);

}

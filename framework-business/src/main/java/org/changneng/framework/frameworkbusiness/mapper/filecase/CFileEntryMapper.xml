<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.changneng.framework.frameworkbusiness.dao.filecase.CFileEntryMapper">
  <resultMap id="BaseResultMap" type="org.changneng.framework.frameworkbusiness.entity.filecase.CFileEntry">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="ENTRY_NAME" jdbcType="VARCHAR" property="entryName" />
    <result column="LOCATION" jdbcType="DECIMAL" property="location" />
    <result column="IS_DELETE" jdbcType="DECIMAL" property="isDelete" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, ENTRY_NAME, LOCATION, IS_DELETE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from C_FILE_ENTRY
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from C_FILE_ENTRY
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteAll" parameterType="java.lang.String">
    delete from C_FILE_ENTRY
  </delete>
  <insert id="insert" parameterType="org.changneng.framework.frameworkbusiness.entity.filecase.CFileEntry">
    insert into C_FILE_ENTRY (ID, ENTRY_NAME, LOCATION, 
      IS_DELETE)
    values (#{id,jdbcType=VARCHAR}, #{entryName,jdbcType=VARCHAR}, #{location,jdbcType=DECIMAL}, 
      #{isDelete,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" parameterType="org.changneng.framework.frameworkbusiness.entity.filecase.CFileEntry">
  	<selectKey keyProperty="id" order="BEFORE" resultType="String">
  		select sys_guid() from dual
  	</selectKey>
    insert into C_FILE_ENTRY
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="entryName != null">
        ENTRY_NAME,
      </if>
      <if test="location != null">
        LOCATION,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="entryName != null">
        #{entryName,jdbcType=VARCHAR},
      </if>
      <if test="location != null">
        #{location,jdbcType=DECIMAL},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="org.changneng.framework.frameworkbusiness.entity.filecase.CFileEntry">
    update C_FILE_ENTRY
    <set>
      <if test="entryName != null">
        ENTRY_NAME = #{entryName,jdbcType=VARCHAR},
      </if>
      <if test="location != null">
        LOCATION = #{location,jdbcType=DECIMAL},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=DECIMAL},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.changneng.framework.frameworkbusiness.entity.filecase.CFileEntry">
    update C_FILE_ENTRY
    set ENTRY_NAME = #{entryName,jdbcType=VARCHAR},
      LOCATION = #{location,jdbcType=DECIMAL},
      IS_DELETE = #{isDelete,jdbcType=DECIMAL}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
   <select id="selectAll" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from C_FILE_ENTRY
    ORDER BY LOCATION
  </select>
  <select id="selectByIdAndName" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from C_FILE_ENTRY where 1=1
    and  ENTRY_NAME = #{entryName,jdbcType=VARCHAR}
    <if test="entryId!=null and entryId!=''">
    	AND ID &lt;&gt;#{entryId,jdbcType=VARCHAR}
    </if>
  </select>
  <select id="selectMaxLocation"  resultType="java.lang.Integer">
  	select max(location) from C_FILE_ENTRY
  </select>
  <select id="selectAvailableDataList" resultMap="BaseResultMap">
  	SELECT 
  	<include refid="Base_Column_List"/>
  	FROM C_FILE_ENTRY  ORDER BY LOCATION
  </select>
</mapper>
package org.changneng.framework.frameworkbusiness.entity;

public class AskingDefaultModel {
    private String id;

    private String userid;

    private String customDatabaseId;

    private String templateObjectType;
    
    private String templateType;//2017/11/29新增字段
    
    public String getTemplateType() {
		return templateType;
	}

	public void setTemplateType(String templateType) {
		this.templateType = templateType;
	}
    
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid == null ? null : userid.trim();
    }

    public String getCustomDatabaseId() {
        return customDatabaseId;
    }

    public void setCustomDatabaseId(String customDatabaseId) {
        this.customDatabaseId = customDatabaseId == null ? null : customDatabaseId.trim();
    }

    public String getTemplateObjectType() {
        return templateObjectType;
    }

    public void setTemplateObjectType(String templateObjectType) {
        this.templateObjectType = templateObjectType == null ? null : templateObjectType.trim();
    }
}
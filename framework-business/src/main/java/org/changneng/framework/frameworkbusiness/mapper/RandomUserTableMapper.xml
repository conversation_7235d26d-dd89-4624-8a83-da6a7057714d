<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.changneng.framework.frameworkbusiness.dao.RandomUserTableMapper">
  <resultMap id="BaseResultMap" type="org.changneng.framework.frameworkbusiness.entity.RandomUserTable">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="AREA_CODE" jdbcType="VARCHAR" property="areaCode" />
    <result column="DEPARTMENT_ID" jdbcType="VARCHAR" property="departmentId" />
    <result column="DEPARTMENT_NAME" jdbcType="VARCHAR" property="departmentName" />
    <result column="USER_ID" jdbcType="VARCHAR" property="userId" />
    <result column="USER_NAME" jdbcType="VARCHAR" property="userName" />
    <result column="CARDID" jdbcType="VARCHAR" property="cardid" />
    <result column="LAW_DEVICE_ID" jdbcType="VARCHAR" property="lawDeviceId" />
    <result column="LAW_ENFORC_ID" jdbcType="VARCHAR" property="lawEnforcId" />
    <result column="SUPERVISION_CERTIFICATE_ID" jdbcType="VARCHAR" property="supervisionCertificateId" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, AREA_CODE, DEPARTMENT_ID, DEPARTMENT_NAME, USER_ID, USER_NAME, CARDID, LAW_DEVICE_ID, 
    LAW_ENFORC_ID, SUPERVISION_CERTIFICATE_ID
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from RANDOM_USER_TABLE
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from RANDOM_USER_TABLE
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="org.changneng.framework.frameworkbusiness.entity.RandomUserTable">
    insert into RANDOM_USER_TABLE (ID, AREA_CODE, DEPARTMENT_ID, 
      DEPARTMENT_NAME, USER_ID, USER_NAME, 
      CARDID, LAW_DEVICE_ID, LAW_ENFORC_ID, 
      SUPERVISION_CERTIFICATE_ID)
    values (#{id,jdbcType=VARCHAR}, #{areaCode,jdbcType=VARCHAR}, #{departmentId,jdbcType=VARCHAR}, 
      #{departmentName,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR}, #{userName,jdbcType=VARCHAR}, 
      #{cardid,jdbcType=VARCHAR}, #{lawDeviceId,jdbcType=VARCHAR}, #{lawEnforcId,jdbcType=VARCHAR}, 
      #{supervisionCertificateId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="org.changneng.framework.frameworkbusiness.entity.RandomUserTable">
    insert into RANDOM_USER_TABLE
      <selectKey keyProperty="id" order="BEFORE" resultType="String">
  		select sys_guid() from dual
  	</selectKey>
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="areaCode != null">
        AREA_CODE,
      </if>
      <if test="departmentId != null">
        DEPARTMENT_ID,
      </if>
      <if test="departmentName != null">
        DEPARTMENT_NAME,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="userName != null">
        USER_NAME,
      </if>
      <if test="cardid != null">
        CARDID,
      </if>
      <if test="lawDeviceId != null">
        LAW_DEVICE_ID,
      </if>
      <if test="lawEnforcId != null">
        LAW_ENFORC_ID,
      </if>
      <if test="supervisionCertificateId != null">
        SUPERVISION_CERTIFICATE_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="areaCode != null">
        #{areaCode,jdbcType=VARCHAR},
      </if>
      <if test="departmentId != null">
        #{departmentId,jdbcType=VARCHAR},
      </if>
      <if test="departmentName != null">
        #{departmentName,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="cardid != null">
        #{cardid,jdbcType=VARCHAR},
      </if>
      <if test="lawDeviceId != null">
        #{lawDeviceId,jdbcType=VARCHAR},
      </if>
      <if test="lawEnforcId != null">
        #{lawEnforcId,jdbcType=VARCHAR},
      </if>
      <if test="supervisionCertificateId != null">
        #{supervisionCertificateId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="org.changneng.framework.frameworkbusiness.entity.RandomUserTable">
    update RANDOM_USER_TABLE
    <set>
      <if test="areaCode != null">
        AREA_CODE = #{areaCode,jdbcType=VARCHAR},
      </if>
      <if test="departmentId != null">
        DEPARTMENT_ID = #{departmentId,jdbcType=VARCHAR},
      </if>
      <if test="departmentName != null">
        DEPARTMENT_NAME = #{departmentName,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        USER_ID = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        USER_NAME = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="cardid != null">
        CARDID = #{cardid,jdbcType=VARCHAR},
      </if>
      <if test="lawDeviceId != null">
        LAW_DEVICE_ID = #{lawDeviceId,jdbcType=VARCHAR},
      </if>
      <if test="lawEnforcId != null">
        LAW_ENFORC_ID = #{lawEnforcId,jdbcType=VARCHAR},
      </if>
      <if test="supervisionCertificateId != null">
        SUPERVISION_CERTIFICATE_ID = #{supervisionCertificateId,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.changneng.framework.frameworkbusiness.entity.RandomUserTable">
    update RANDOM_USER_TABLE
    set AREA_CODE = #{areaCode,jdbcType=VARCHAR},
      DEPARTMENT_ID = #{departmentId,jdbcType=VARCHAR},
      DEPARTMENT_NAME = #{departmentName,jdbcType=VARCHAR},
      USER_ID = #{userId,jdbcType=VARCHAR},
      USER_NAME = #{userName,jdbcType=VARCHAR},
      CARDID = #{cardid,jdbcType=VARCHAR},
      LAW_DEVICE_ID = #{lawDeviceId,jdbcType=VARCHAR},
      LAW_ENFORC_ID = #{lawEnforcId,jdbcType=VARCHAR},
      SUPERVISION_CERTIFICATE_ID = #{supervisionCertificateId,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <!-- deleteRandomUserTable 删除表数据 -->
  <delete id="deleteRandomUserTable">
  		   delete from RANDOM_USER_TABLE
  </delete>
  <!--selectRandomUserTableByDepartmentId根据部门id查询人员  -->
      <select id="selectRandomUserTableByDepartmentId" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from RANDOM_USER_TABLE
    where DEPARTMENT_ID in (${departmentId})
  </select>
<!-- deleteRandomUserManager 根据人员得id和部门的id删除用户信息  -->
  <delete id="deleteRandomUserManager" parameterType="java.lang.String">
    delete from RANDOM_USER_TABLE 
    where USER_ID = #{userId} and DEPARTMENT_ID =#{departmentId}
  </delete>
  <!--  seleteRandomUserByDepAndUserId 更具用户的id和部门的id查询用户的信息-  -->
  <select id="seleteRandomUserByDepAndUserId" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
     ID
    from RANDOM_USER_TABLE
    where USER_ID = #{userId} and DEPARTMENT_ID =#{departmentId}
  </select>
  <!-- insertrandomUserTableLdID初始化 执法证号人员 -->
  <insert id="insertrandomUserTableLdID">
	INSERT  into RANDOM_USER_TABLE
	(ID,AREA_CODE,DEPARTMENT_ID,DEPARTMENT_NAME,USER_ID,USER_NAME,CARDID,LAW_DEVICE_ID,LAW_ENFORC_ID,SUPERVISION_CERTIFICATE_ID )
	SELECT sys_guid(), BELONG_AREA_ID AS AREA_CODE ,
		SS.DEPARTMENT_ID AS DEPARTMENT_ID,
		BELONG_DEPARTMENT_NAME AS DEPARTMENT_NAME,
		ID AS USER_ID,LOGINNAME AS USER_NAEM,
		CARDID,LAW_DEVICE_ID,
		LAW_ENFORC_ID,SUPERVISION_CERTIFICATE_ID
	FROM SYS_USERS , 
	 (
	 	SELECT DEPARTMENT_ID  
		from SYS_DEPARTMENT sd 
		where sd.ISLAWDEPT ='1'  and sd.DEPARTMENT_STATE ='1'   
	 ) SS
	WHERE ENABLED = 1 and 
	    ACCOUNT_NON_LAWOFFICER = 1
			and LAW_CERTIFICATE_ENABLED =1 and LAW_ENFORC_ID is not null
			and ACCOUNT_NON_LOCKED = 1 
	 AND (
			BELONG_DEPARTMENT_ID IN (
				SELECT department_id 
				FROM sys_department 
				START WITH parent_deptid= SS.DEPARTMENT_ID
				CONNECT BY PRIOR  department_id=parent_deptid)
	OR  BELONG_DEPARTMENT_ID in(
			SELECT department_id 
			FROM sys_department 
			where  sys_department.department_id=SS.DEPARTMENT_ID))
  </insert>
  <!-- insertRandomUserTableScID初始化检查证号 -->
  <insert id="insertRandomUserTableScID">
	 INSERT  into RANDOM_USER_TABLE
	(ID,AREA_CODE,DEPARTMENT_ID,DEPARTMENT_NAME,USER_ID,USER_NAME,CARDID,LAW_DEVICE_ID,LAW_ENFORC_ID,SUPERVISION_CERTIFICATE_ID )
	SELECT sys_guid(), BELONG_AREA_ID AS AREA_CODE ,
		SS.DEPARTMENT_ID AS DEPARTMENT_ID,
		BELONG_DEPARTMENT_NAME AS DEPARTMENT_NAME,
		ID AS USER_ID,LOGINNAME AS USER_NAEM,
		CARDID,LAW_DEVICE_ID,SUPERVISION_CERTIFICATE_ID as LAW_ENFORC_ID,
		SUPERVISION_CERTIFICATE_ID
	 FROM SYS_USERS , 
	 (
	 	SELECT DEPARTMENT_ID  
		from SYS_DEPARTMENT sd 
		where sd.ISLAWDEPT ='1'  and sd.DEPARTMENT_STATE ='1'   
	)SS
	WHERE ENABLED = 1 and 
	    ACCOUNT_NON_LAWOFFICER = 1
			and LAW_CERTIFICATE_ENABLED =0 and SUPERVISIO_CERTIFICATE_ENABLED =1 and SUPERVISION_CERTIFICATE_ID is not null
			and ACCOUNT_NON_LOCKED = 1 
	 AND (
			BELONG_DEPARTMENT_ID IN (
				SELECT department_id 
				FROM sys_department 
				START WITH parent_deptid= SS.DEPARTMENT_ID
				CONNECT BY PRIOR  department_id=parent_deptid)
	OR  BELONG_DEPARTMENT_ID in(
			SELECT department_id 
			FROM sys_department 
			where  sys_department.department_id=SS.DEPARTMENT_ID))
  </insert>
</mapper>
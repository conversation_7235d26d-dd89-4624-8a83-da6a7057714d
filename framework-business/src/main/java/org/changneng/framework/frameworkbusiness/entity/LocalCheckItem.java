package org.changneng.framework.frameworkbusiness.entity;

import java.util.Date;
import java.util.List;

import org.changneng.framework.frameworkbusiness.entity.refinetemplate.SceneCheckEntry;
import org.springframework.format.annotation.DateTimeFormat;

public class LocalCheckItem  {
    private String id;

    private String checkItemName;

    private String checkItemResult;

    private String taskId;

    private String localCheckId;

    private String remark;

    private String sceneItemDatabaseId;

    private Date createTime;

    private Date updateTime;

    private Date startDate;
    private Date endDate;

    private String startDateStr;
    private String endDateStr;

    private String sceneCustomItemId;

    private Integer loction;

    private String behId;//对接我要环保 违法行为id
    private String behFact;

    private String dateType;

    private String checkItemStatus; //0单选，1多选，2输入，3下拉
    private String checkitemType; //多选check_item_Checkbox ，下拉check_item_select

    private String gisCoordinateX;
    private  String gisCoordinateY;
    private  String sceneSysItemId;

    private List<SceneCheckEntry> sceneCheckEntryList;

    private List<Boolean> checkboxResult;

   // private List<TcDictionary> tcList; //多选和下拉绑定值


	/**
	 * 检查项类型：0=原有检查项，1=环境监管一件事
	 */
	private Integer formType;

	/**
	 * 关联CHECK_ITEM_CONFIG表ID
	 */
	private String configItemId;

	/**
	 * 问题简述
	 */
	private String problemDesc;

	/**
	 * 检查项配置树形结构ID，关联CHECK_ITEM_CONFIG表主键
	 */
	private String treeConfigId;

	public Integer getFormType() {
		return formType;
	}

	public void setFormType(Integer formType) {
		this.formType = formType;
	}

	public String getConfigItemId() {
		return configItemId;
	}

	public void setConfigItemId(String configItemId) {
		this.configItemId = configItemId;
	}

	public String getProblemDesc() {
		return problemDesc;
	}

	public void setProblemDesc(String problemDesc) {
		this.problemDesc = problemDesc;
	}

	public String getGisCoordinateX() {
		return gisCoordinateX;
	}

	public String getSceneSysItemId() {
		return sceneSysItemId;
	}

	public void setSceneSysItemId(String sceneSysItemId) {
		this.sceneSysItemId = sceneSysItemId;
	}

	public void setGisCoordinateX(String gisCoordinateX) {
		this.gisCoordinateX = gisCoordinateX;
	}

	public String getGisCoordinateY() {
		return gisCoordinateY;
	}

	public void setGisCoordinateY(String gisCoordinateY) {
		this.gisCoordinateY = gisCoordinateY;
	}

	public String getStartDateStr() {
		return startDateStr;
	}

	public void setStartDateStr(String startDateStr) {
		this.startDateStr = startDateStr;
	}

	public String getEndDateStr() {
		return endDateStr;
	}

	public void setEndDateStr(String endDateStr) {
		this.endDateStr = endDateStr;
	}

	public List<Boolean> getCheckboxResult() {
		return checkboxResult;
	}

	public void setCheckboxResult(List<Boolean> checkboxResult) {
		this.checkboxResult = checkboxResult;
	}

	public Date getStartDate() {
		return startDate;
	}

	public String getDateType() {
		return dateType;
	}

	public void setDateType(String dateType) {
		this.dateType = dateType;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}




	private String checkedName;


    public String getBehFact() {
		return behFact;
	}

	public void setBehFact(String behFact) {
		this.behFact = behFact;
	}

	public String getCheckedName() {
		return checkedName;
	}

	public void setCheckedName(String checkedName) {
		this.checkedName = checkedName;
	}

	public List<SceneCheckEntry> getSceneCheckEntryList() {
		return sceneCheckEntryList;
	}

	public void setSceneCheckEntryList(List<SceneCheckEntry> sceneCheckEntryList) {
		this.sceneCheckEntryList = sceneCheckEntryList;
	}




	private String isMust;//是否为必填


	public String getIsMust() {
		return isMust;
	}

	public void setIsMust(String isMust) {
		this.isMust = isMust;
	}

	public String getCheckItemStatus() {
		return checkItemStatus;
	}

	public void setCheckItemStatus(String checkItemStatus) {
		this.checkItemStatus = checkItemStatus;
	}

	public String getCheckitemType() {
		return checkitemType;
	}

	public void setCheckitemType(String checkitemType) {
		this.checkitemType = checkitemType;
	}

	public String getBehId() {
		return behId;
	}

	public void setBehId(String behId) {
		this.behId = behId;
	}

	public Integer getLoction() {
		return loction;
	}

	public void setLoction(Integer loction) {
		this.loction = loction;
	}

	public String getSceneCustomItemId() {
		return sceneCustomItemId;
	}

	public void setSceneCustomItemId(String sceneCustomItemId) {
		this.sceneCustomItemId = sceneCustomItemId;
	}

	public String getId()  {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getCheckItemName() {
        return checkItemName;
    }

    public void setCheckItemName(String checkItemName) {
        this.checkItemName = checkItemName == null ? null : checkItemName.trim();
    }

    public String getCheckItemResult() {
        return checkItemResult;
    }

    public void setCheckItemResult(String checkItemResult) {
        this.checkItemResult = checkItemResult == null ? null : checkItemResult.trim();
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId == null ? null : taskId.trim();
    }

    public String getLocalCheckId() {
        return localCheckId;
    }

    public void setLocalCheckId(String localCheckId) {
        this.localCheckId = localCheckId == null ? null : localCheckId.trim();
    }

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getSceneItemDatabaseId() {
		return sceneItemDatabaseId;
	}

	public void setSceneItemDatabaseId(String sceneItemDatabaseId) {
		this.sceneItemDatabaseId = sceneItemDatabaseId;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}



}
